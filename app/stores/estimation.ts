import { defineStore } from 'pinia'
import type { InstrumentList } from "../types/instruments/instrument";
import type { EstimationItem } from "../types/estimation";

export interface EstimationState {
    items: EstimationItem[]
    isOpen: boolean
}

export const useEstimationStore = defineStore('estimation', {
    state: (): EstimationState => ({
        items: [],
        isOpen: false
    }),

    getters: {
        totalItems: (state): number => {
            return state.items.reduce((total, item) => total + item.quantity, 0)
        },

        hasItems: (state): boolean => {
            return state.items.length > 0
        }
    },

    actions: {
        // Save to localStorage
        saveToStorage() {
            if (typeof window !== 'undefined') { // Simple check for browser
                localStorage.setItem('mulster-estimation', JSON.stringify({
                    items: this.items,
                    isOpen: this.isOpen
                }))
            }
        },

        // Load from localStorage
        loadFromStorage() {
            if (typeof window !== 'undefined') {
                const saved = localStorage.getItem('mulster-estimation')
                if (saved) {
                    try {
                        const data = JSON.parse(saved)
                        this.items = data.items || []
                        this.isOpen = data.isOpen || false
                    } catch (error) {
                        console.warn('Error loading estimation from storage:', error)
                    }
                }
            }
        },

        hasInstrument(instrumentId: string): boolean {
            return this.items.some(item => item.instrument.id === instrumentId)
        },

        getInstrumentQuantity(instrumentId: string): number {
            const item = this.items.find(item => item.instrument.id === instrumentId)
            return item?.quantity || 0
        },

        addInstrument(instrument: InstrumentList, quantity: number = 1) {
            const existingItem = this.items.find(item => item.instrument.id === instrument.id)

            if (existingItem) {
                existingItem.quantity += quantity
            } else {
                this.items.push({
                    id: `temp_${Date.now()}_${instrument.id}`,
                    instrument,
                    quantity
                })
            }

            this.saveToStorage()
        },

        removeInstrument(instrumentId: string) {
            const index = this.items.findIndex(item => item.instrument.id === instrumentId)
            if (index > -1) {
                this.items.splice(index, 1)
            }
            this.saveToStorage()
        },

        updateQuantity(instrumentId: string, quantity: number) {
            const item = this.items.find(item => item.instrument.id === instrumentId)
            if (item) {
                if (quantity <= 0) {
                    this.removeInstrument(instrumentId)
                } else {
                    item.quantity = quantity
                }
            }
            this.saveToStorage()
        },

        clearEstimation() {
            this.items = []
            this.saveToStorage()
        },

        toggleSidebar() {
            this.isOpen = !this.isOpen
            this.saveToStorage()
        },

        openSidebar() {
            this.isOpen = true
            this.saveToStorage()
        },

        closeSidebar() {
            this.isOpen = false
            this.saveToStorage()
        }
    }
})