import { defineStore } from 'pinia'

export const useUIStore = defineStore('ui', {
    state: () => ({
        isSearchActive: false,
        isBurgerMenuOpen: false,
        isProfileDropdownOpen: false
    }),

    actions: {
        toggleSearch() {
            this.isSearchActive = !this.isSearchActive
            // Ferme les autres menus
            this.isBurgerMenuOpen = false
            this.isProfileDropdownOpen = false
        },

        toggleBurgerMenu() {
            this.isBurgerMenuOpen = !this.isBurgerMenuOpen
            // Ferme les autres menus
            this.isSearchActive = false
            this.isProfileDropdownOpen = false
        },

        toggleProfileDropdown() {
            this.isProfileDropdownOpen = !this.isProfileDropdownOpen
        },

        closeAll() {
            this.isSearchActive = false
            this.isBurgerMenuOpen = false
            this.isProfileDropdownOpen = false
        }
    }
})