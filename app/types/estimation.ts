import type { InstrumentList } from './instruments/instrument'
import type { LocationZone } from "./locationZone";

export enum EstimationStatus {
    DRAFT = 'DRAFT',
    SAVED = 'SAVED',
    SUBMITTED = 'SUBMITTED',
    ORDERED = 'ORDERED',
    FINISHED = 'FINISHED',
    CANCELLED = 'CANCELLED',
    DELETED = 'DELETED'
}

export enum EstimationEventType {
    CONCERT   = 'CONCERT',
    RESIDENCE = 'RESIDENCE',
    WORKSHOP  = 'WORKSHOP'
}

export enum EstimationConditionType {
    INTERIOR  = 'INTERIOR',
    EXTERIOR  = 'EXTERIOR'
}

export enum EstimationTechnicianType {
    DELIVERY        = 'DELIVERY',
    INSTALLATION    = 'INSTALLATION',
    EVENT_PRESENCE  = 'EVENT_PRESENCE'
}

export enum EstimationAccessibilityType {
    STAIRS        = 'STAIRS',
    ELEVATOR      = 'ELEVATOR',
    GROUND_FLOOR  = 'GROUND_FLOOR'
}

export interface EstimationItem {
    id: string
    instrument: InstrumentList
    quantity: number
}

export interface Estimation {
    id: string,
    startAt?: string
    endAt?: string
    status: EstimationStatus,
    totalDays?: number
    calculatedMinPrice?: string
    calculatedMaxPrice?: string
    calculatedAt?: string
    conditionType?: EstimationConditionType
    isNearBeach?: boolean
    accessibilityTypes?: EstimationAccessibilityType[]
    estimationItems: EstimationItem[]
    eventType?: EstimationEventType
    reference?: string
    eventName?: string
    needsTechnician?: boolean
    technicianTypes?: EstimationTechnicianType[]
    additionalInformation?: string
    locationZone?: LocationZone
}