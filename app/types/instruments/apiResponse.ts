import type { InstrumentCategory, InstrumentDetail, InstrumentList } from "./instrument";
import type { Breadcrumb, Filters, Meta } from "./meta";

export interface FilteredInstrumentsResponse {
    has_result: boolean,
    instruments: InstrumentList[],
    meta: Meta,
    filters: Filters,
    message?: string
}

export interface SearchInstrumentsResponse {
    has_result: boolean,
    instruments: InstrumentList[],
    meta: Meta,
    message?: string
}

export interface GetOneInstrumentResponse {
    error: boolean,
    instrument: InstrumentDetail,
    breadcrumb: Breadcrumb[],
    title?: string
}

export interface GetCategoriesResponse {
    error: boolean,
    breadcrumb: Breadcrumb[],
    categories: InstrumentCategory[],
}