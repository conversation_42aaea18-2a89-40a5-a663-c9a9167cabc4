import type { Picture } from "../picture";

export interface InstrumentList {
    id: string,
    name: string,
    type: string,
    model: string,
    instrument_brand: InstrumentBrand,
    instrument_category: InstrumentCategory,
    base_price_per_day?: string,
    price?: string,
    slug: string,
    main_picture?: Picture
}

export interface InstrumentDetail extends InstrumentList {
    description?: string,
    links: string[],
    related_instruments?: InstrumentList[],
    accessories?: InstrumentList[],
    length_cm?: string,
    height_cm?: string,
    width_cm?: string,
    weight_kg?: string,
    pictures?: Picture[]
}

export interface InstrumentBrand {
    id: string
    name: string
}

export interface InstrumentCategory {
    id: string
    name: string
    slug: string
    fullPath?: string
    picture?: Picture
}