import type { Inst<PERSON>ent<PERSON><PERSON> } from "./instrument";

export interface Meta {
    total_items: number,
    page: number,
    total_pages: number,
    next?:	string,
    search_term?: string,
    suggestions?: Suggestions[]
}

export interface Breadcrumb {
    id: string
    slug: string
    position: number
    name: string
}

export interface Filters {
    category?: [
        id: string,
        name: string
    ],
    brands?: string[],
    search?: string,
    filtered_brands_by_category?: InstrumentBrand[]
}

export interface Suggestions {
    uri: string,
    label: string,
    similarity: number
}