import { categoryService } from '~/services/api/categoryService'
import type { GetCategoriesResponse } from '~/types/instruments/apiResponse'

export const useCategories = () => {
    // Direct service calls
    const { getAll, getRootCategories, getChildrenCategory } = categoryService

    // Cached versions for components (recommandé pour le header)
    const getAllCached = () =>
        useLazyFetch<GetCategoriesResponse>('all-categories', () => categoryService.getAll())

    const getRootCategoriesCached = () =>
        useLazyFetch<GetCategoriesResponse>('root-categories', () => categoryService.getRootCategories())

    const getChildrenCategoryCached = (categorySlug: string) =>
        useLazyFetch<GetCategoriesResponse>(
            `children-category-${categorySlug}`,
            () => categoryService.getChildrenCategory(categorySlug)
        )

    return {
        // Direct calls
        getAll,
        getRootCategories,
        getChildrenCategory,

        // Cached calls (to avoid repetitive calls)
        getAllCached,
        getRootCategoriesCached,
        getChildrenCategoryCached
    }
}