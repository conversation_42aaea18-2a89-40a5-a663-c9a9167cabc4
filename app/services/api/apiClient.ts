interface ApiClientOptions {
    baseURL?: string
    headers?: Record<string, string>
}

interface RequestOptions {
    requireAuth?: boolean
    token?: string
    params?: any
    body?: any
}

export const createApiClient = (options: ApiClientOptions = {}) => {
    const defaultOptions = {
        baseURL: options.baseURL || "http://mulster-back/api/",
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...options.headers
        }
    }

    const buildHeaders = (requireAuth: boolean = false, customToken?: string) => {
        const headers = { ...defaultOptions.headers }

        if (requireAuth) {
            let token = customToken

            // Si pas de token passé, essaie de le récupérer automatiquement
            if (!token) {
                // TODO: À adapter selon ton store d'auth
                // const authStore = useAuthStore()
                // token = authStore.token

                // Ou depuis un cookie/localStorage
                if (process.client) {
                    token = localStorage.getItem('auth-token')
                }
            }

            if (token) {
                headers['Authorization'] = `Bearer ${token}`
            }
        }

        return headers
    }

    const request = async <T>(method: 'GET' | 'POST' | 'PUT' | 'DELETE', endpoint: string, options: RequestOptions = {}): Promise<T> => {
        const { requireAuth = false, token, params, body } = options

        return await $fetch<T>(endpoint, {
            method,
            baseURL: defaultOptions.baseURL,
            headers: buildHeaders(requireAuth, token),
            params,
            body
        })
    }

    return {
        get: <T>(endpoint: string, options: RequestOptions = {}) =>
            request<T>('GET', endpoint, options),

        post: <T>(endpoint: string, options: RequestOptions = {}) =>
            request<T>('POST', endpoint, options),

        put: <T>(endpoint: string, options: RequestOptions = {}) =>
            request<T>('PUT', endpoint, options),

        delete: <T>(endpoint: string, options: RequestOptions = {}) =>
            request<T>('DELETE', endpoint, options)
    }
}

export const apiClient = createApiClient()