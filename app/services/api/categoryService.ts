import type { GetCategoriesResponse } from '~/types/instruments/apiResponse'
import {apiClient} from "~/services/api/apiClient";

export const categoryService = {

    /**
     * Get all categories
     */
    async getAll(): Promise<GetCategoriesResponse> {
        return await apiClient.get<GetCategoriesResponse>('/instrument/category/all')
    },

    /**
     * Get root categories (categories without parent)
     */
    async getRootCategories(): Promise<GetCategoriesResponse> {
        return await apiClient.get<GetCategoriesResponse>('/instrument/category/root')
    },

    /**
     * Get children of a specific category
     * @param categoryId - The slug of the parent category
     */
    async getChildrenCategory(categoryId: string): Promise<GetCategoriesResponse> {
        return await apiClient.get<GetCategoriesResponse>(`/instrument/category/${categoryId}/children`)
    }
}