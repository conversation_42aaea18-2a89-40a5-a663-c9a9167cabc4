<template>
    <div>
        <h1>Toute la logistique musicale, au même endroit.</h1>

        <!-- Test simple de notre store -->
        <div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
            <h2>Test du store Estimation</h2>
            <p>Nombre d'articles dans l'estimation : {{ totalItems }}</p>
            <p>Sidebar ouverte : {{ isOpen ? 'Oui' : 'Non' }}</p>

            <button @click="testAddInstrument">
                Ajouter un instrument test
            </button>

            <button @click="estimationStore.toggleSidebar()">
                Toggle Sidebar
            </button>

            <button @click="estimationStore.clearEstimation()">
                Vider l'estimation
            </button>

            <!-- Liste des articles -->
            <div v-if="estimationStore.hasItems" style="margin-top: 20px;">
                <h3>Articles dans l'estimation :</h3>
                <ul>
                    <li v-for="item in estimationStore.items" :key="item.id">
                        {{ item.instrument.name }} - Quantité : {{ item.quantity }}
                        <button @click="estimationStore.removeInstrument(item.instrument.id)">
                            Supprimer
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <p>
            <NuxtLink to="/categories">Voir les catégories</NuxtLink>
            |
        </p>
    </div>
</template>

<script setup lang="ts">
import type {InstrumentList} from "~/types/instruments/instrument";
import {useEstimationStore} from "~/stores/estimation";
import { computed, onMounted } from "vue";

// Use our store
const estimationStore = useEstimationStore()

// Load data from localStorage when component mounts
onMounted(() => {
    estimationStore.loadFromStorage()
})

// Computed properties
const totalItems = computed(() => estimationStore.totalItems)
const isOpen = computed(() => estimationStore.isOpen)

// Test function to add a fake instrument
const testAddInstrument = () => {
    const fakeInstrument: InstrumentList = {
        id: `test_${Date.now()}`,
        name: 'Guitare Test',
        type: 'Électrique',
        model: 'Stratocaster',
        slug: 'guitare-test',
        instrument_brand: {
            id: '1',
            name: 'Fender'
        },
        instrument_category: {
            id: '1',
            name: 'Guitares',
            slug: 'guitares'
        },
        price: '50'
    }

    estimationStore.addInstrument(fakeInstrument, 1)
}
</script>