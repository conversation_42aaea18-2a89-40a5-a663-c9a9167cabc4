<!-- components/layout/Header/HeaderBar.vue -->
<template>
    <div class="header-bar">
        <!-- <PERSON> (caché pendant la recherche) -->
        <button
            v-show="!uiStore.isSearchActive"
            class="header-btn burger-btn"
            @click="uiStore.toggleBurgerMenu"
            :class="{ active: uiStore.isBurgerMenuOpen }"
        >
            <PhosphorIcon name="list" :size="24" />
        </button>

        <!-- Logo ou Search Bar -->
        <div class="header-center" :class="{ 'search-expanded': uiStore.isSearchActive }">
            <!-- Logo (visible si search pas active) -->
            <NuxtLink
                v-if="!uiStore.isSearchActive"
                to="/"
                class="logo"
            >
                <img src="/img/mulster_logo.png" alt="Mulster" class="logo-image" />
            </NuxtLink>

            <!-- Search Bar (visible si search active) -->
            <div
                v-else
                class="search-bar"
            >
                <input
                    ref="searchInput"
                    v-model="searchQuery"
                    type="text"
                    placeholder="Rechercher un instrument..."
                    class="search-input"
                    @blur="onSearchBlur"
                    @keyup.enter="performSearch"
                />
            </div>
        </div>

        <!-- Right Actions -->
        <div class="header-actions">
            <!-- Search Button -->
            <button
                class="header-btn search-btn"
                @click="toggleSearch"
                :class="{ active: uiStore.isSearchActive }"
            >
                <PhosphorIcon v-if="!uiStore.isSearchActive" name="magnifying-glass" :size="24" />
                <PhosphorIcon v-else name="x" :size="24" />
            </button>

            <!-- Profile Button -->
            <button
                class="header-btn profile-btn"
                @click="uiStore.toggleProfileDropdown"
                :class="{ active: uiStore.isProfileDropdownOpen }"
            >
                <PhosphorIcon name="user" :size="24" />
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { useUIStore } from '~/stores/ui'

const uiStore = useUIStore()
const router = useRouter()

const searchQuery = ref('')
const searchInput = ref<HTMLInputElement>()

const toggleSearch = async () => {
    uiStore.toggleSearch()

    if (uiStore.isSearchActive) {
        await nextTick()
        searchInput.value?.focus()
    } else {
        searchQuery.value = ''
    }
}

const onSearchBlur = () => {
    setTimeout(() => {
        if (!searchQuery.value.trim()) {
            uiStore.isSearchActive = false
        }
    }, 150)
}

const performSearch = () => {
    if (searchQuery.value.trim()) {
        router.push(`/catalogue?search=${encodeURIComponent(searchQuery.value)}`)
        uiStore.closeAll()
    }
}
</script>

<style scoped>
.header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: white;
    transition: all 0.3s ease-in-out;
}

.header-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border: none;
    background: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    color: #333;
}

.header-btn:hover,
.header-btn.active {
    background-color: #f5f5f5;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    margin: 0 12px;
    transition: all 0.3s ease-in-out;
}

.header-center.search-expanded {
    margin: 0 8px 0 16px;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: opacity 0.3s ease-in-out;
}

.logo-image {
    height: 32px; /* Ajuste selon ta taille d'image */
    width: auto;
    max-width: 120px; /* Limite la largeur */
    object-fit: contain;
}

.search-bar {
    width: 100%;
    max-width: 400px;
    transition: all 0.3s ease-in-out;
}

.search-input {
    width: 100%;
    padding: 12px 20px;
    border: 2px solid #e5e5e5;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-input:focus {
    border-color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-actions {
    display: flex;
    gap: 4px;
}
</style>