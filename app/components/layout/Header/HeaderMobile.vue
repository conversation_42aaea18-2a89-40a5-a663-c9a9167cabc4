<!-- components/layout/Header/HeaderMobile.vue -->
<template>
    <header class="header-mobile">
        <HeaderBar />
        <HeaderCategories />

        <!-- Burger Menu avec animation -->
        <Transition name="slide">
            <BurgerMenu v-if="uiStore.isBurgerMenuOpen" @close="uiStore.closeAll" />
        </Transition>

        <ProfileDropdown v-if="uiStore.isProfileDropdownOpen" @close="uiStore.closeAll" />

        <!-- Backdrop animé -->
        <Transition name="backdrop">
            <div
                v-if="uiStore.isBurgerMenuOpen || uiStore.isProfileDropdownOpen"
                class="backdrop"
                @click="uiStore.closeAll"
            />
        </Transition>
    </header>
</template>

<script setup lang="ts">
import { useUIStore } from '~/stores/ui'
import HeaderBar from './HeaderBar.vue'
import HeaderCategories from './HeaderCategories.vue'
import BurgerMenu from './BurgerMenu.vue'
import ProfileDropdown from './ProfileDropdown.vue'

const uiStore = useUIStore()

// Close menus on route change
const router = useRouter()
router.afterEach(() => {
    uiStore.closeAll()
})
</script>

<style scoped>
.header-mobile {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    border-bottom: 1px solid #e5e5e5;
}

.backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1001;
}

/* Animations du backdrop */
.backdrop-enter-active,
.backdrop-leave-active {
    transition: opacity 0.3s ease-in-out;
}

.backdrop-enter-from,
.backdrop-leave-to {
    opacity: 0;
}

/* Animations du burger menu (slide depuis la gauche) */
.slide-enter-active,
.slide-leave-active {
    transition: transform 0.3s ease-in-out;
}

.slide-enter-from,
.slide-leave-to {
    transform: translateX(-100%);
}
</style>