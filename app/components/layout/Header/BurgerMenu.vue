<!-- components/layout/Header/BurgerMenu.vue -->
<template>
    <div class="burger-menu">
        <div class="menu-content">
            <!-- Header du menu -->
            <div class="menu-header">
                <button class="close-btn" @click="$emit('close')">
                    <PhosphorIcon name="x" :size="24"/>
                </button>
                <h3>Menu</h3>
            </div>

            <!-- Navigation principale -->
            <nav class="menu-nav">
                <NuxtLink to="/" class="nav-item" @click="$emit('close')">
                    <PhosphorIcon name="house" :size="20" />
                    Accueil
                </NuxtLink>

                <NuxtLink to="/" class="nav-item" @click="$emit('close')">
                    <PhosphorIcon name="squares-four" :size="20" />
                    Toutes les catégories
                </NuxtLink>

                <NuxtLink to="/" class="nav-item" @click="$emit('close')">
                    <PhosphorIcon name="list" :size="20" />
                    Catalogue
                </NuxtLink>
            </nav>

            <div class="menu-divider" />

            <!-- Informations -->
            <div class="menu-section">
                <h4>Informations</h4>

                <NuxtLink to="/" class="nav-item" @click="$emit('close')">
                    <PhosphorIcon name="info" :size="20" />
                    À propos
                </NuxtLink>

                <NuxtLink to="/" class="nav-item" @click="$emit('close')">
                    <PhosphorIcon name="leaf" :size="20" />
                    Notre impact local
                </NuxtLink>

                <NuxtLink to="/" class="nav-item" @click="$emit('close')">
                    <PhosphorIcon name="scroll" :size="20" />
                    Fanzine
                </NuxtLink>
            </div>

            <!-- Contact info -->
            <div class="menu-footer">
                <div class="contact-info">
                    <p><strong>Besoin d'aide ?</strong></p>
                    <p>📞 04 XX XX XX XX</p>
                    <p>📧 <EMAIL></p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const emit = defineEmits<{
    close: []
}>()
</script>

<style scoped>
.burger-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: white;
    z-index: 1002;
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
    /* Pas d'animation CSS ici, on utilise Vue Transition */
}

.menu-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.menu-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border-bottom: 1px solid #e5e5e5;
}

.menu-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    border-radius: 8px;
    cursor: pointer;
    color: #666;
    margin-right: ;
}

.close-btn:hover {
    background: #f5f5f5;
}

.menu-nav {
    padding: 20px 0;
}

.menu-section {
    padding: 0 0 20px 0;
}

.menu-section h4 {
    margin: 0 0 12px 0;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s;
}

.nav-item:hover {
    background-color: #f5f5f5;
}

.menu-divider {
    height: 1px;
    background: #e5e5e5;
    margin-bottom: 20px;
}

.menu-footer {
    margin-top: auto;
    padding: 20px;
    border-top: 1px solid #e5e5e5;
    background: #fafafa;
}

.contact-info p {
    margin: 4px 0;
    font-size: 14px;
    color: #666;
}

.contact-info p:first-child {
    color: #333;
    margin-bottom: 8px;
}
</style>