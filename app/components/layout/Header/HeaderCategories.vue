<!-- components/layout/Header/HeaderCategories.vue -->
<template>
    <div class="header-categories">
        <div class="categories-scroll" ref="scrollContainer">
            <div class="categories-list">
                <!-- Loading state -->
                <div v-if="pending" class="categories-loading">
                    <div v-for="n in 5" :key="n" class="category-skeleton" />
                </div>

                <!-- Categories -->
                <NuxtLink
                    v-else
                    v-for="category in categories"
                    :key="category.id"
                    :to="`/categories/${category.slug}`"
                    class="category-item"
                    :class="{ active: isActiveCategory(category.slug) }"
                >
                    {{ category.name }}
                </NuxtLink>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useCategories } from '~/composables/useCategories'

const route = useRoute()

// Get root categories with cache
const { data: categoriesResponse, pending } = await useCategories().getRootCategoriesCached()

const categories = computed(() => categoriesResponse.value?.categories || [])

const isActiveCategory = (slug: string) => {
    return route.path.startsWith(`/categories/${slug}`)
}
</script>

<style scoped>
.header-categories {
    background: #fafafa;
    border-bottom: 1px solid #e5e5e5;
}

.categories-scroll {
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

.categories-scroll::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

.categories-list {
    display: flex;
    padding: 12px 16px;
    gap: 8px;
    min-width: max-content;
}

.category-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: white;
    border: 1px solid #e5e5e5;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    text-decoration: none;
    white-space: nowrap;
    transition: all 0.2s;
}

.category-item:hover,
.category-item.active {
    background: #333;
    color: white;
    border-color: #333;
}

.categories-loading {
    display: flex;
    gap: 8px;
}

.category-skeleton {
    width: 80px;
    height: 34px;
    background: #e5e5e5;
    border-radius: 20px;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}
</style>