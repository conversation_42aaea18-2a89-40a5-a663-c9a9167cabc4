<!-- components/layout/Header/ProfileDropdown.vue -->
<template>
    <div class="profile-dropdown">
        <div class="dropdown-content">
            <!-- User connecté -->
            <div v-if="isConnected" class="user-section">
                <div class="user-info">
                    <PhosphorIcon name="user" :size="20"/>
                    <span>{{ userName }}</span>
                </div>

                <NuxtLink to="#" class="dropdown-item" @click="$emit('close')">
                    <PhosphorIcon name="user" :size="18"/>
                    Mon compte
                </NuxtLink>

                <button class="dropdown-item" @click="createEstimation">
                    <PhosphorIcon name="plus" :size="18"/>
                    Créer une estimation
                </button>

                <div class="dropdown-divider"/>

                <button class="dropdown-item logout-item" @click="logout">
                    <PhosphorIcon name="sign-out" :size="18"/>
                    Déconnexion
                </button>
            </div>

            <!-- User non connecté -->
            <div v-else class="guest-section">
                <button class="dropdown-item primary" @click="createEstimation">
                    <PhosphorIcon name="plus" :size="18"/>
                    Créer une estimation
                </button>

                <NuxtLink to="/login" class="dropdown-item" @click="$emit('close')">
                    <PhosphorIcon name="sign-in" :size="18"/>
                    Se connecter
                </NuxtLink>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import {computed} from 'vue'

const emit = defineEmits<{
    close: []
}>()

const router = useRouter()

// TODO: Replace with your actual auth store/composable
const isConnected = computed(() => false) // Replace with real auth state
const userName = computed(() => 'John Doe') // Replace with real user name

const createEstimation = () => {
    router.push('/estimation/infos')
    emit('close')
}

const logout = () => {
    // TODO: Implement logout logic
    console.log('Logout user')
    emit('close')
}
</script>

<style scoped>
    .profile-dropdown {
        position: absolute;
        top: 62%;
        right: 16px;
        z-index: 1002;
        margin-top: 8px;
    }

    .dropdown-content {
        background: white;
        border: 1px solid #e5e5e5;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        min-width: 200px;
        overflow: hidden;
    }

    .user-section,
    .guest-section {
        padding: 8px;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        background: #f9f9f9;
        margin: -8px -8px 8px -8px;
        font-weight: 500;
        color: #333;
    }

    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 12px;
        width: 100%;
        padding: 12px 16px;
        border: none;
        background: none;
        font-size: 14px;
        color: #333;
        text-decoration: none;
        cursor: pointer;
        border-radius: 8px;
        transition: background-color 0.2s;
    }

    .dropdown-item:hover {
        background-color: #f5f5f5;
    }

    .dropdown-item.primary {
        background: #333;
        color: white;
    }

    .dropdown-item.primary:hover {
        background: #555;
    }

    .dropdown-item.logout-item {
        color: #dc2626;
    }

    .dropdown-item.logout-item:hover {
        background-color: #fef2f2;
    }

    .dropdown-divider {
        height: 1px;
        background: #e5e5e5;
        margin: 8px 0;
    }
</style>