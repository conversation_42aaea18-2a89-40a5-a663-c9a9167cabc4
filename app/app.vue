<!-- app.vue -->
<template>
    <div id="app">
        <HeaderMobile />
        <main class="main-content">
            <NuxtPage />
        </main>
    </div>
</template>

<script setup>
import HeaderMobile from '~/components/layout/Header/HeaderMobile.vue'
</script>

<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #fff;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
}
</style>