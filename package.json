{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/eslint": "^1.9.0", "@nuxt/image": "^1.11.0", "@pinia/nuxt": "^0.11.2", "eslint": "^9.36.0", "nuxt": "^4.1.2", "nuxt-phosphor-icons": "^2.3.2", "pinia": "^3.0.3", "vue": "^3.5.21", "vue-router": "^4.5.1"}, "devDependencies": {"sass": "^1.93.1"}}